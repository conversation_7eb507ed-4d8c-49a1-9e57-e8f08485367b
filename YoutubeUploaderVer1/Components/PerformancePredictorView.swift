//
//  PerformancePredictorView.swift
//  YoutubeUploaderVer1
//
//  Created by AI Assistant on 08/06/25.
//

import SwiftUI


struct PerformancePredictorView: View {
    let title: String
    let description: String
    let category: String
    let thumbnailDescription: String
    
    @StateObject private var analyzer = PerformancePredictorAnalyzer()
    @State private var showCopyAlert = false
    @State private var copiedText = ""
    
    var body: some View {
        VStack(alignment: .leading, spacing: 20) {
            // Header
            headerSection

            // Analysis Progress
            if analyzer.isAnalyzing {
                analysisProgressSection
            }

            // Results
            if let prediction = analyzer.prediction, !analyzer.isAnalyzing {
                resultsSection(prediction: prediction)
            }

            // Error Section
            if let error = analyzer.errorMessage, !analyzer.isAnalyzing {
                errorSection(error: error)
            }

            // Predict Button
            if !analyzer.isAnalyzing {
                predictButtonSection
            }
        }
        .padding(24)
        .background(AppColor.darkGrayBackground.color.opacity(0.5))
        .cornerRadius(16)
        .overlay(
            RoundedRectangle(cornerRadius: 16)
                .stroke(AppColor.grayText.color.opacity(0.1), lineWidth: 1)
        )
        .alert("Copied!", isPresented: $showCopyAlert) {
            Button("OK") { }
        } message: {
            Text("Performance data copied to clipboard successfully!")
        }
        .onChange(of: analyzer.isAnalyzing) { isAnalyzing in
            if isAnalyzing {
                NotificationCenter.default.post(name: NSNotification.Name("PerformancePredictionStarted"), object: nil)
            } else {
                NotificationCenter.default.post(name: NSNotification.Name("PerformancePredictionCompleted"), object: nil)
            }
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        HStack {
            Image(systemName: "chart.line.uptrend.xyaxis")
                .font(.system(size: 24))
                .foregroundColor(AppColor.youtubeRed.color)

            VStack(alignment: .leading, spacing: 4) {
                Text("Performance Predictor")
                    .font(AppFontStyle.headline.style.weight(.bold))
                    .foregroundColor(AppColor.primary.color)

                Text("Predict how your video will perform before publishing")
                    .font(AppFontStyle.subheadline.style)
                    .foregroundColor(AppColor.grayText.color)
            }

            Spacer()
        }
    }
    
    // MARK: - Predict Button Section
    private var predictButtonSection: some View {
        VStack(spacing: 12) {
            // Info message when requirements not met
            if title.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty || description.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                HStack(spacing: 8) {
                    Image(systemName: "info.circle.fill")
                        .font(.system(size: 16))
                        .foregroundColor(.blue)

                    Text("Please fill out the video title and description to enable performance prediction")
                        .font(AppFontStyle.caption1.style)
                        .foregroundColor(AppColor.grayText.color)
                        .fixedSize(horizontal: false, vertical: true)

                    Spacer()
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(Color.blue.opacity(0.1))
                .cornerRadius(8)
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(Color.blue.opacity(0.3), lineWidth: 1)
                )
            }

            Button(action: {
                Task {
                    await analyzer.predictPerformance(
                        title: title,
                        description: description,
                        category: category,
                        thumbnailDescription: thumbnailDescription
                    )
                }
            }) {
                HStack(spacing: 12) {
                    Image(systemName: "chart.line.uptrend.xyaxis")
                        .font(.system(size: 18))

                    Text("Predict Performance")
                        .font(AppFontStyle.headline.style.weight(.semibold))
                }
                .padding(.horizontal, 32)
                .padding(.vertical, 16)
                .frame(maxWidth: .infinity)
                .background(isButtonEnabled ? AppColor.youtubeRed.color : AppColor.grayText.color.opacity(0.3))
                .foregroundColor(isButtonEnabled ? .white : AppColor.grayText.color)
                .cornerRadius(12)
                .shadow(color: isButtonEnabled ? AppColor.youtubeRed.color.opacity(0.4) : Color.clear, radius: isButtonEnabled ? 8 : 0, x: 0, y: isButtonEnabled ? 4 : 0)
            }
            .buttonStyle(.plain)
            .disabled(!isButtonEnabled)
        }
    }

    private var isButtonEnabled: Bool {
        !title.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
        !description.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }
    

    
    // MARK: - Analysis Progress Section
    private var analysisProgressSection: some View {
        VStack(spacing: 16) {
            HStack {
                ProgressView()
                    .scaleEffect(0.8)
                    .progressViewStyle(CircularProgressViewStyle(tint: AppColor.youtubeRed.color))

                VStack(alignment: .leading, spacing: 4) {
                    Text("Analyzing Performance...")
                        .font(AppFontStyle.headline.style.weight(.semibold))
                        .foregroundColor(AppColor.primary.color)

                    Text(analyzer.currentStep)
                        .font(AppFontStyle.subheadline.style)
                        .foregroundColor(AppColor.grayText.color)
                }

                Spacer()
            }

            ProgressView(value: analyzer.analysisProgress)
                .progressViewStyle(LinearProgressViewStyle(tint: AppColor.youtubeRed.color))
                .scaleEffect(y: 2)
        }
        .padding(20)
        .background(AppColor.darkGrayBackground.color)
        .cornerRadius(12)
    }
    
    // MARK: - Results Section
    private func resultsSection(prediction: PerformancePrediction) -> some View {
        VStack(alignment: .leading, spacing: 20) {
            // Performance Overview
            performanceOverview(prediction: prediction)
            
            // Detailed Metrics
            detailedMetrics(metrics: prediction.metrics)
            
            // Performance Factors
            performanceFactors(factors: prediction.factors)

        }
    }
    
    // MARK: - Performance Overview
    private func performanceOverview(prediction: PerformancePrediction) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Performance Prediction")
                .font(AppFontStyle.headline.style.weight(.bold))
                .foregroundColor(AppColor.primary.color)
            
            HStack(spacing: 16) {
                // Overall Score
                VStack(spacing: 8) {
                    ZStack {
                        Circle()
                            .stroke(AppColor.grayText.color.opacity(0.3), lineWidth: 8)
                            .frame(width: 80, height: 80)
                        
                        Circle()
                            .trim(from: 0, to: prediction.overallScore / 100)
                            .stroke(prediction.category.color, lineWidth: 8)
                            .frame(width: 80, height: 80)
                            .rotationEffect(.degrees(-90))
                        
                        VStack(spacing: 2) {
                            Text("\(Int(prediction.overallScore))")
                                .font(AppFontStyle.title3.style.weight(.bold))
                                .foregroundColor(AppColor.primary.color)
                            
                            Text("Score")
                                .font(AppFontStyle.caption2.style)
                                .foregroundColor(AppColor.grayText.color)
                        }
                    }
                    
                    VStack(spacing: 4) {
                        HStack(spacing: 6) {
                            Image(systemName: prediction.category.icon)
                                .font(.system(size: 14, weight: .semibold))
                                .foregroundColor(prediction.category.color)
                            
                            Text(prediction.category.rawValue)
                                .font(AppFontStyle.caption1.style.weight(.semibold))
                                .foregroundColor(prediction.category.color)
                        }
                        
                        Text("\(Int(prediction.confidence))% Confidence")
                            .font(AppFontStyle.caption2.style)
                            .foregroundColor(AppColor.grayText.color)
                    }
                }
                
                Spacer()
                
                // Key Metrics
                VStack(alignment: .leading, spacing: 12) {
                    metricRow(
                        icon: "eye.fill",
                        title: "Estimated Views",
                        value: prediction.metrics.estimatedViews.displayRange,
                        color: .blue
                    )
                    
                    metricRow(
                        icon: "heart.fill",
                        title: "Engagement Rate",
                        value: "\(String(format: "%.1f", prediction.metrics.engagementRate))%",
                        color: .red
                    )
                    
                    metricRow(
                        icon: "clock.fill",
                        title: "Retention Rate",
                        value: "\(String(format: "%.1f", prediction.metrics.retentionRate))%",
                        color: .green
                    )
                    
                    metricRow(
                        icon: "flame.fill",
                        title: "Viral Potential",
                        value: "\(Int(prediction.metrics.viralPotential))/100",
                        color: .purple
                    )
                }
            }
        }
        .padding(16)
        .background(AppColor.darkGrayBackground.color.opacity(0.3))
        .cornerRadius(12)
    }
    
    private func metricRow(icon: String, title: String, value: String, color: Color) -> some View {
        HStack(spacing: 8) {
            Image(systemName: icon)
                .font(.system(size: 12, weight: .semibold))
                .foregroundColor(color)
                .frame(width: 16)
            
            Text(title)
                .font(AppFontStyle.caption1.style)
                .foregroundColor(AppColor.grayText.color)
            
            Spacer()
            
            Text(value)
                .font(AppFontStyle.caption1.style.weight(.semibold))
                .foregroundColor(AppColor.primary.color)
        }
    }
    
    // MARK: - Detailed Metrics
    private func detailedMetrics(metrics: PerformanceMetrics) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Detailed Metrics")
                .font(AppFontStyle.headline.style.weight(.bold))
                .foregroundColor(AppColor.primary.color)
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                metricCard(
                    title: "Click-Through Rate",
                    value: "\(String(format: "%.1f", metrics.clickThroughRate))%",
                    icon: "hand.tap.fill",
                    color: .orange
                )
                
                metricCard(
                    title: "Algorithm Score",
                    value: "\(Int(metrics.algorithmScore))/100",
                    icon: "cpu.fill",
                    color: .cyan
                )
            }
        }
    }
    
    private func metricCard(title: String, value: String, icon: String, color: Color) -> some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.system(size: 20, weight: .semibold))
                .foregroundColor(color)
            
            Text(value)
                .font(AppFontStyle.subheadline.style.weight(.bold))
                .foregroundColor(AppColor.primary.color)
            
            Text(title)
                .font(AppFontStyle.caption1.style)
                .foregroundColor(AppColor.grayText.color)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding(12)
        .background(AppColor.darkGrayBackground.color.opacity(0.3))
        .cornerRadius(12)
    }
    
    // MARK: - Performance Factors
    private func performanceFactors(factors: PerformanceFactors) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Performance Factors")
                .font(AppFontStyle.headline.style.weight(.bold))
                .foregroundColor(AppColor.primary.color)
            
            VStack(spacing: 8) {
                factorBar(title: "Title Strength", value: factors.titleStrength, color: .blue)
                factorBar(title: "Description Quality", value: factors.descriptionQuality, color: .green)
                factorBar(title: "Topic Trending", value: factors.topicTrending, color: .purple)
                factorBar(title: "Competition Level", value: 100 - factors.competitionLevel, color: .orange) // Show as opportunity
                factorBar(title: "Upload Timing", value: factors.uploadTiming, color: .cyan)
                factorBar(title: "Thumbnail Potential", value: factors.thumbnailPotential, color: .red)
            }
        }
        .padding(16)
        .background(AppColor.darkGrayBackground.color.opacity(0.2))
        .cornerRadius(12)
    }
    
    private func factorBar(title: String, value: Double, color: Color) -> some View {
        VStack(alignment: .leading, spacing: 4) {
            HStack {
                Text(title)
                    .font(AppFontStyle.caption1.style)
                    .foregroundColor(AppColor.grayText.color)
                
                Spacer()
                
                Text("\(Int(value))/100")
                    .font(AppFontStyle.caption1.style.weight(.semibold))
                    .foregroundColor(AppColor.primary.color)
            }
            
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    Rectangle()
                        .fill(AppColor.grayText.color.opacity(0.2))
                        .frame(height: 6)
                        .cornerRadius(3)
                    
                    Rectangle()
                        .fill(color)
                        .frame(width: geometry.size.width * (value / 100), height: 6)
                        .cornerRadius(3)
                }
            }
            .frame(height: 6)
        }
    }

    
    // MARK: - Error Section
    private func errorSection(error: String) -> some View {
        VStack(spacing: 16) {
            HStack(spacing: 12) {
                Image(systemName: "exclamationmark.triangle.fill")
                    .font(.system(size: 24))
                    .foregroundColor(.orange)

                VStack(alignment: .leading, spacing: 4) {
                    Text("Prediction Failed")
                        .font(AppFontStyle.headline.style.weight(.semibold))
                        .foregroundColor(AppColor.primary.color)

                    Text(error)
                        .font(AppFontStyle.subheadline.style)
                        .foregroundColor(AppColor.grayText.color)
                }

                Spacer()
            }
        }
        .padding(20)
        .background(AppColor.darkGrayBackground.color.opacity(0.3))
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.orange.opacity(0.3), lineWidth: 1)
        )
    }
    

    
    // MARK: - Helper Methods
    private func copyRecommendations(_ recommendations: [String]) {
        let text = recommendations.enumerated().map { index, recommendation in
            "\(index + 1). \(recommendation)"
        }.joined(separator: "\n\n")
    
        NSPasteboard.general.setString(text, forType: .string)
    
        
        copiedText = text
        showCopyAlert = true
    }
}
